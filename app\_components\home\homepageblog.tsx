"use client"
import bloggroup from "@/public/new assests/news icons/heroicons/homeimage/bloggroup.png"
import laptoptable from "@/public/new assests/news icons/heroicons/homeimage/laptoptable.png"
import arrowblog from "@/public/new assests/news icons/heroicons/homeimage/arrowcircleblog.svg"
import Image from "next/image";

// Dummy blog data
const blogData = [
  {
    id: 1,
    title: "Spending Habits, 13 Tips for grow Your Money.",
    author: "Rashed Ka",
    date: "18 Jul 2024",
    image: bloggroup,
    readTime: "5 min read"
  },
  {
    id: 2,
    title: "Our Travel Card Makes you Happy and save Your Money.",
    author: "Rashed Ka",
    date: "18 Jul 2024",
    image: laptoptable,
    readTime: "3 min read"
  }
];

export default function HomePageBlog() {
  return (
    <div className="text-white  w-full flex flex-col justify-center items-center">
      <div className="w-[80%] px-10 flex flex-col gap-y-10 mt-10">
        {/* Header Section */}
        <div className="flex justify-between items-center ">
          <div>
            <h2 className="text-[78px] md:text-[78px] lg:text-[78px] font-bold font-satoshi leading-tight">
              <span className="p-4 bg-[#2B2B2B] mb-8 rounded-tl-xl rounded-tr-xl rounded-br-xl">Latest Insights</span>
              <br />
              <span className="px-4 bg-[#2B2B2B] rounded-tl-xl rounded-tr-xl rounded-br-xl rounded-bl-xl">& News</span>
            </h2>
          </div>
          <div>
            <button className="bg-transparent border border-white text-white px-6 py-3 rounded-full text-[24px] font-medium hover:bg-white hover:text-black transition-all duration-300">
              Explore More
            </button>
          </div>
        </div>

        {/* Blog Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-[30px] md:gap-[38px] lg:gap-[48px]">
          {blogData.map((blog) => (
            <div
              key={blog.id}
              className="bg-[#2B2B2B] hover:bg-[#363636] rounded-[30px] cursor-pointer p-6 flex flex-col justify-between relative overflow-hidden transition-all duration-300"
            >
              {/* Blog Image */}
              <div className="relative h-[381px] md:h-[381px] lg:h-[381px] rounded-[30px] overflow-hidden mb-6">
                <Image
                  src={blog.image}
                  alt={blog.title}
                  className="object-cover w-full h-full"
                />
                {/* Arrow Icon - Bottom Right */}
                <div className="absolute bottom-4 right-4">
                  <Image
                    src={arrowblog}
                    alt="Arrow"
                    className="w-[61.45px] h-[60.69px]"
                  />
                </div>
              </div>

              {/* Blog Content */}
              <div className="flex flex-col justify-between gap-y-5">
                 {/* Author and Date Info */}
                <div className="flex flex-row justify-between items-center ">
                  <div className="flex flex-row justify-center items-center gap-3">
                      <p className="text-white text-[18px] font-medium">{blog.author}</p>
                      <p className="text-white/60 text-[18px]">{blog.date}</p>
                  </div>
                </div>

                <div>
                  <h3 className="text-[32px] md:text-[32px] lg:text-[32px] font-bold text-white leading-tight mb-3">
                    {blog.title}
                  </h3>
                </div>

               
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}