"use client"
import React, { useState } from 'react'
import Image from 'next/image'
import { BsArrowRight } from "react-icons/bs"

// Dummy blog data
const blogData = [
  {
    id: 1,
    title: "Spending Habits, 13 Tips for grow Your Money.",
    author: "Rashed Ka",
    date: "18 Jul 2024",
    image: "/new assests/blog/coin.jpg",
    readTime: "5 min read",
    category: "Finance"
  },
  {
    id: 2,
    title: "iOS App Development: Complete Guide for Beginners",
    author: "Rashed Ka",
    date: "18 Jul 2024",
    image: "/new assests/blog/ios.jpeg",
    readTime: "3 min read",
    category: "Mobile Development"
  },
  {
    id: 3,
    title: "Digital Marketing Strategies for Modern Business",
    author: "<PERSON>",
    date: "15 Jul 2024",
    image: "/new assests/blog/company1.jpg",
    readTime: "7 min read",
    category: "Marketing"
  },
  {
    id: 4,
    title: "Building Strong Company Culture in Tech",
    author: "<PERSON>",
    date: "12 Jul 2024",
    image: "/new assests/blog/company2.jpg",
    readTime: "6 min read",
    category: "Business"
  },
  {
    id: 5,
    title: "UI/UX Design Trends That Will Dominate",
    author: "<PERSON>",
    date: "10 Jul 2024",
    image: "/new assests/blog/company3.jpg",
    readTime: "4 min read",
    category: "Design"
  },
  {
    id: 6,
    title: "Building Scalable Applications with Next.js",
    author: "Sarah Wilson",
    date: "08 Jul 2024",
    image: "/new assests/blog/company4.jpg",
    readTime: "8 min read",
    category: "Development"
  },
  {
    id: 7,
    title: "Featured Software Development Practices",
    author: "Alex Kumar",
    date: "05 Jul 2024",
    image: "/assest/blog/featured.png",
    readTime: "6 min read",
    category: "Software"
  },
  {
    id: 8,
    title: "Advanced Software Architecture Patterns",
    author: "Priya Sharma",
    date: "02 Jul 2024",
    image: "/assest/blog/softblog.png",
    readTime: "9 min read",
    category: "Architecture"
  },
  {
    id: 9,
    title: "Featured Technologies in Modern Development",
    author: "Rahul Singh",
    date: "28 Jun 2024",
    image: "/assest/blog/featured (1).png",
    readTime: "5 min read",
    category: "Technology"
  }
]

export default function BlogCards() {
  const [currentPage, setCurrentPage] = useState(1)
  const blogsPerPage = 6
  const totalPages = Math.ceil(blogData.length / blogsPerPage)

  const indexOfLastBlog = currentPage * blogsPerPage
  const indexOfFirstBlog = indexOfLastBlog - blogsPerPage
  const currentBlogs = blogData.slice(indexOfFirstBlog, indexOfLastBlog)

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber)
  }

  return (
    <div className="text-white w-full flex flex-col justify-center items-center py-20">
      <div className="w-[90%]  px-4">
        {/* Header Section */}
        

        {/* Blog Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          {currentBlogs.map((blog) => (
            <div
              key={blog.id}
              className=" rounded-[30px] cursor-pointer p-6 flex flex-col justify-between relative overflow-hidden transition-all duration-300 group"
            >
              {/* Blog Image */}
              <div className="relative w-[450px] h-[420px] rounded-[20px] overflow-hidden mb-6">
                <Image
                  src={blog.image}
                  alt={blog.title}
                  width={450}
                  height={420}
                  className="object-cover w-full h-full" //imageUrl of blog
                />
                {/* Arrow Icon - Top Right */}
                <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="bg-white/20 backdrop-blur-sm rounded-full p-2">
                    <BsArrowRight className="w-5 h-5 text-white" />
                  </div>
                </div>
              </div>

              {/* Blog Content */}
              <div className="flex flex-col gap-4">
                {/* Category and Read Time */}
                <div className="flex justify-start items-start gap-3 text-white/50">
                  <span className="text-white/30 text-[17px] font-medium">{blog.category} </span> |
                   <p className="text-white/30 text-[17px]"> {blog.date}</p>
                </div>

                {/* Title */}
                <h3 className="text-[24px] font-bold text-white leading-tight line-clamp-2">
                  {blog.title} 
                  {/*  */}
                </h3>

               
              </div>
            </div>
          ))}
        </div>

        {/* Pagination */}
        <div className="flex justify-center items-center gap-4">
          {/* Previous Button - Only show when not on first page */}
          {currentPage > 1 && (
            <button
              onClick={() => handlePageChange(Math.max(1, currentPage - 1))}
              className="px-4 py-2 rounded-lg transition-all duration-300 bg-[#2B2B2B] text-white hover:bg-[#363636]"
            >
              Previous
            </button>
          )}

          {/* Page Numbers */}
          {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNumber) => (
            <button
              key={pageNumber}
              onClick={() => handlePageChange(pageNumber)}
              className={`w-12 h-12 rounded-lg transition-all duration-300 ${
                currentPage === pageNumber
                  ? 'border border-white hover:bg-white hover:text-black text-white font-bold'
                  : 'border border-white/30 text-white '
              }`}
            >
              {pageNumber}
            </button>
          ))}

          {/* Next Button */}
          <button
            onClick={() => handlePageChange(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className={`px-4 py-2 rounded-lg transition-all duration-300 ${
              currentPage === totalPages
                ? 'bg-[#2B2B2B] text-white/40 cursor-not-allowed'
                : 'bg-[#2B2B2B] text-white hover:bg-[#363636]'
            }`}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  )
}