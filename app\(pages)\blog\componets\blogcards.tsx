"use client"
import React, { useState, useEffect } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { BsArrowRight, BsSearch } from "react-icons/bs"
import { FaFacebook, FaInstagram, FaLinkedin } from "react-icons/fa"
import { getAllBlogs, getBlogCategories, getRecentBlogs, searchBlogs, formatBlogDate, getSocialShareUrls, BlogData } from '@/api/blogs/blogs_api'

export default function BlogCards() {
  const router = useRouter()
  const [blogs, setBlogs] = useState<BlogData[]>([])
  const [categories, setCategories] = useState<any[]>([])
  const [recentBlogs, setRecentBlogs] = useState<BlogData[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [showAllCategories, setShowAllCategories] = useState(false)
  const [showAllKeywords, setShowAllKeywords] = useState(false)
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalCount, setTotalCount] = useState(0)

  // Fetch blogs data
  useEffect(() => {
    fetchBlogs()
    fetchCategories()
    fetchRecentBlogs()
  }, [currentPage, searchQuery, selectedCategory])

  const fetchBlogs = async () => {
    try {
      setLoading(true)
      const response = await getAllBlogs({
        page: currentPage,
        limit: 6,
        search: searchQuery || undefined,
        category: selectedCategory || undefined
      })

      if (response.status) {
        setBlogs(response.data.data)
        setTotalPages(response.data.totalPages)
        setTotalCount(response.data.totalCount)
      }
    } catch (error) {
      console.error('Error fetching blogs:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await getBlogCategories()
      if (response.status) {
        setCategories(response.data)
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }

  const fetchRecentBlogs = async () => {
    try {
      const response = await getRecentBlogs(3)
      if (response.status) {
        setRecentBlogs(response.data.data)
      }
    } catch (error) {
      console.error('Error fetching recent blogs:', error)
    }
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    setCurrentPage(1)
    fetchBlogs()
  }

  const handleCategoryFilter = (category: string) => {
    setSelectedCategory(category === selectedCategory ? '' : category)
    setCurrentPage(1)
  }

  const handleBlogClick = (blogId: string, title: string) => {
    const slug = title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
    router.push(`/blog/${blogId}/${slug}`)
  }

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber)
  }

  return (
    <div className="text-white w-full flex justify-center py-20 bg-black">
      <div className="w-[95%] max-w-[1400px] flex gap-8">
        {/* Left Side - Blog Cards */}
        <div className="flex-1">
          {loading ? (
            <div className="text-center py-20">
              <div className="text-white">Loading blogs...</div>
            </div>
          ) : (
            <>
              {/* Blog Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-16">
                {blogs.map((blog) => (
                  <div
                    key={blog._id}
                    onClick={() => handleBlogClick(blog._id, blog.title)}
                    className="bg-zinc-900 rounded-[20px] cursor-pointer p-0 flex flex-col relative overflow-hidden transition-all duration-300 group hover:scale-105"
                  >
                    {/* Blog Image */}
                    <div className="relative w-full h-[300px] rounded-t-[20px] overflow-hidden">
                      <Image
                        src={blog.imageUrl}
                        alt={blog.title}
                        width={500}
                        height={300}
                        className="object-cover w-full h-full"
                      />
                      {/* Date - Top Left */}
                      <div className="absolute top-4 left-4 bg-black/70 backdrop-blur-sm rounded-lg px-3 py-1">
                        <span className="text-white text-sm font-medium">
                          {formatBlogDate(blog.createdAt)}
                        </span>
                      </div>
                      {/* Arrow Icon - Top Right */}
                      <div className="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                        <div className="bg-white/20 backdrop-blur-sm rounded-full p-2">
                          <BsArrowRight className="w-5 h-5 text-white" />
                        </div>
                      </div>
                    </div>

                    {/* Blog Content */}
                    <div className="p-6 flex flex-col gap-4 flex-1">
                      {/* Category with Hover Dropdown */}
                      <div className="relative group/category">
                        <div className="flex items-center gap-2">
                          <span className="bg-orange-500 text-white px-3 py-1 rounded-full text-sm">
                            {blog.category[0]}
                          </span>
                          {blog.category.length > 1 && (
                            <span className="text-white/50 text-sm cursor-pointer">
                              +{blog.category.length - 1} more
                            </span>
                          )}
                        </div>
                        {/* Dropdown for additional categories */}
                        {blog.category.length > 1 && (
                          <div className="absolute top-full left-0 mt-2 bg-zinc-800 rounded-lg p-3 opacity-0 group-hover/category:opacity-100 transition-opacity duration-300 z-10 min-w-[200px]">
                            <div className="flex flex-wrap gap-2">
                              {blog.category.slice(1).map((cat, index) => (
                                <span key={index} className="bg-orange-500/80 text-white px-2 py-1 rounded-full text-xs">
                                  {cat}
                                </span>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>

                      {/* Title */}
                      <h3 className="text-[20px] font-bold text-white leading-tight line-clamp-2">
                        {blog.title}
                      </h3>

                      {/* Description */}
                      <p className="text-white/70 text-sm line-clamp-3">
                        {blog.description}
                      </p>

                      {/* Bottom Section */}
                      <div className="flex justify-between items-end mt-auto pt-4">
                        {/* Keywords - Only show first 2 */}
                        <div className="flex flex-wrap gap-2">
                          {blog.keywords.slice(0, 2).map((keyword, index) => (
                            <span key={index} className="bg-zinc-700 text-white/80 px-2 py-1 rounded text-xs">
                              {keyword.replace(/[\[\]"]/g, '')}
                            </span>
                          ))}
                        </div>

                        {/* Social Share Icons */}
                        <div className="flex gap-2">
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              const shareUrls = getSocialShareUrls(blog._id, blog.title, window.location.href)
                              window.open(shareUrls.facebook, '_blank')
                            }}
                            className="p-2 bg-zinc-700 hover:bg-blue-600 rounded-full transition-colors"
                          >
                            <FaFacebook className="w-4 h-4 text-white" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              const shareUrls = getSocialShareUrls(blog._id, blog.title, window.location.href)
                              window.open(shareUrls.instagram, '_blank')
                            }}
                            className="p-2 bg-zinc-700 hover:bg-pink-600 rounded-full transition-colors"
                          >
                            <FaInstagram className="w-4 h-4 text-white" />
                          </button>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              const shareUrls = getSocialShareUrls(blog._id, blog.title, window.location.href)
                              window.open(shareUrls.linkedin, '_blank')
                            }}
                            className="p-2 bg-zinc-700 hover:bg-blue-700 rounded-full transition-colors"
                          >
                            <FaLinkedin className="w-4 h-4 text-white" />
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </>
          )}
        </div>

        {/* Right Sidebar */}
        <div className="w-[350px] flex flex-col gap-8">
          {/* Search Section */}
          <div className="bg-zinc-900 rounded-[20px] p-6">
            <h3 className="text-white text-[20px] font-bold mb-4">Search</h3>
            <form onSubmit={handleSearch} className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search blogs..."
                className="w-full bg-zinc-800 border border-zinc-700 rounded-lg px-4 py-3 pr-12 text-white placeholder-white/50 focus:outline-none focus:border-orange-500"
              />
              <button
                type="submit"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/70 hover:text-orange-500 transition-colors"
              >
                <BsSearch className="w-5 h-5" />
              </button>
            </form>
          </div>

          {/* Categories Section */}
          <div className="bg-zinc-900 rounded-[20px] p-6">
            <h3 className="text-white text-[20px] font-bold mb-4">Category</h3>
            <div className="space-y-3">
              {categories.slice(0, showAllCategories ? categories.length : 5).map((category, index) => (
                <div
                  key={index}
                  onClick={() => handleCategoryFilter(category.name)}
                  className={`flex justify-between items-center p-3 rounded-lg cursor-pointer transition-colors ${
                    selectedCategory === category.name
                      ? 'bg-orange-500 text-white'
                      : 'bg-zinc-800 text-white/80 hover:bg-zinc-700'
                  }`}
                >
                  <span className="font-medium">{category.name}</span>
                  <span className="text-sm">({category.count})</span>
                </div>
              ))}
              {categories.length > 5 && (
                <button
                  onClick={() => setShowAllCategories(!showAllCategories)}
                  className="w-full text-orange-500 hover:text-orange-400 text-sm font-medium py-2 transition-colors"
                >
                  {showAllCategories ? 'Show Less' : 'More'}
                </button>
              )}
            </div>
          </div>

          {/* Recent News Section */}
          <div className="bg-zinc-900 rounded-[20px] p-6">
            <h3 className="text-white text-[20px] font-bold mb-4">Recent News</h3>
            <div className="space-y-4">
              {recentBlogs.map((blog) => (
                <div
                  key={blog._id}
                  onClick={() => handleBlogClick(blog._id, blog.title)}
                  className="flex gap-3 cursor-pointer group"
                >
                  <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                    <Image
                      src={blog.imageUrl}
                      alt={blog.title}
                      width={64}
                      height={64}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-white text-sm font-medium line-clamp-2 group-hover:text-orange-500 transition-colors">
                      {blog.title}
                    </h4>
                    <p className="text-white/50 text-xs mt-1">
                      {formatBlogDate(blog.createdAt)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Keywords Section */}
          <div className="bg-zinc-900 rounded-[20px] p-6">
            <h3 className="text-white text-[20px] font-bold mb-4">Keywords</h3>
            <div className="flex flex-wrap gap-2">
              {/* Get unique keywords from all blogs */}
              {blogs
                .flatMap(blog => blog.keywords)
                .filter((keyword, index, arr) => arr.indexOf(keyword) === index)
                .slice(0, showAllKeywords ? undefined : 5)
                .map((keyword, index) => (
                  <span
                    key={index}
                    onClick={() => {
                      setSearchQuery(keyword.replace(/[\[\]"]/g, ''))
                      setCurrentPage(1)
                      fetchBlogs()
                    }}
                    className="bg-zinc-800 hover:bg-orange-500 text-white/80 hover:text-white px-3 py-1 rounded-full text-sm cursor-pointer transition-colors"
                  >
                    {keyword.replace(/[\[\]"]/g, '')}
                  </span>
                ))}
              {blogs.flatMap(blog => blog.keywords).length > 5 && (
                <button
                  onClick={() => setShowAllKeywords(!showAllKeywords)}
                  className="text-orange-500 hover:text-orange-400 text-sm font-medium transition-colors"
                >
                  {showAllKeywords ? 'Less' : 'More'}
                </button>
              )}
            </div>
          </div>

          {/* Any Questions Section */}
          <div className="bg-zinc-900 rounded-[20px] p-6">
            <h3 className="text-white text-[20px] font-bold mb-4">Any Questions?</h3>
            <p className="text-white/70 text-sm mb-4">
              Have questions about our services or need help with your project?
            </p>
            <Link
              href="/contact"
              className="w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-3 px-4 rounded-lg transition-colors text-center block"
            >
              Let's Talk
            </Link>
          </div>
        </div>
      </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex justify-center items-center gap-4 mt-8">
              {/* Previous Button */}
              {currentPage > 1 && (
                <button
                  onClick={() => handlePageChange(currentPage - 1)}
                  className="px-4 py-2 rounded-lg transition-all duration-300 bg-zinc-800 text-white hover:bg-zinc-700"
                >
                  Previous
                </button>
              )}

              {/* Page Numbers */}
              {Array.from({ length: totalPages }, (_, i) => i + 1).map((pageNumber) => (
                <button
                  key={pageNumber}
                  onClick={() => handlePageChange(pageNumber)}
                  className={`w-12 h-12 rounded-lg transition-all duration-300 ${
                    currentPage === pageNumber
                      ? 'bg-orange-500 text-white font-bold'
                      : 'bg-zinc-800 text-white hover:bg-zinc-700'
                  }`}
                >
                  {pageNumber}
                </button>
              ))}

              {/* Next Button */}
              {currentPage < totalPages && (
                <button
                  onClick={() => handlePageChange(currentPage + 1)}
                  className="px-4 py-2 rounded-lg transition-all duration-300 bg-zinc-800 text-white hover:bg-zinc-700"
                >
                  Next
                </button>
              )}
            </div>
          )}
        </div>

        {/* Right Sidebar */}
        <div className="w-[350px] flex flex-col gap-8">
          {/* Search Section */}
          <div className="bg-zinc-900 rounded-[20px] p-6">
            <h3 className="text-white text-[20px] font-bold mb-4">Search</h3>
            <form onSubmit={handleSearch} className="relative">
              <input
                type="text"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search blogs..."
                className="w-full bg-zinc-800 border border-zinc-700 rounded-lg px-4 py-3 pr-12 text-white placeholder-white/50 focus:outline-none focus:border-orange-500"
              />
              <button
                type="submit"
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-white/70 hover:text-orange-500 transition-colors"
              >
                <BsSearch className="w-5 h-5" />
              </button>
            </form>
          </div>

          {/* Categories Section */}
          <div className="bg-zinc-900 rounded-[20px] p-6">
            <h3 className="text-white text-[20px] font-bold mb-4">Category</h3>
            <div className="space-y-3">
              {categories.slice(0, showAllCategories ? categories.length : 5).map((category, index) => (
                <div
                  key={index}
                  onClick={() => handleCategoryFilter(category.name)}
                  className={`flex justify-between items-center p-3 rounded-lg cursor-pointer transition-colors ${
                    selectedCategory === category.name
                      ? 'bg-orange-500 text-white'
                      : 'bg-zinc-800 text-white/80 hover:bg-zinc-700'
                  }`}
                >
                  <span className="font-medium">{category.name}</span>
                  <span className="text-sm">({category.count})</span>
                </div>
              ))}
              {categories.length > 5 && (
                <button
                  onClick={() => setShowAllCategories(!showAllCategories)}
                  className="w-full text-orange-500 hover:text-orange-400 text-sm font-medium py-2 transition-colors"
                >
                  {showAllCategories ? 'Show Less' : 'More'}
                </button>
              )}
            </div>
          </div>

          {/* Recent News Section */}
          <div className="bg-zinc-900 rounded-[20px] p-6">
            <h3 className="text-white text-[20px] font-bold mb-4">Recent News</h3>
            <div className="space-y-4">
              {recentBlogs.map((blog) => (
                <div
                  key={blog._id}
                  onClick={() => handleBlogClick(blog._id, blog.title)}
                  className="flex gap-3 cursor-pointer group"
                >
                  <div className="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0">
                    <Image
                      src={blog.imageUrl}
                      alt={blog.title}
                      width={64}
                      height={64}
                      className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                    />
                  </div>
                  <div className="flex-1">
                    <h4 className="text-white text-sm font-medium line-clamp-2 group-hover:text-orange-500 transition-colors">
                      {blog.title}
                    </h4>
                    <p className="text-white/50 text-xs mt-1">
                      {formatBlogDate(blog.createdAt)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Keywords Section */}
          <div className="bg-zinc-900 rounded-[20px] p-6">
            <h3 className="text-white text-[20px] font-bold mb-4">Keywords</h3>
            <div className="flex flex-wrap gap-2">
              {/* Get unique keywords from all blogs */}
              {blogs
                .flatMap(blog => blog.keywords)
                .filter((keyword, index, arr) => arr.indexOf(keyword) === index)
                .slice(0, showAllKeywords ? undefined : 5)
                .map((keyword, index) => (
                  <span
                    key={index}
                    onClick={() => {
                      setSearchQuery(keyword.replace(/[\[\]"]/g, ''))
                      setCurrentPage(1)
                      fetchBlogs()
                    }}
                    className="bg-zinc-800 hover:bg-orange-500 text-white/80 hover:text-white px-3 py-1 rounded-full text-sm cursor-pointer transition-colors"
                  >
                    {keyword.replace(/[\[\]"]/g, '')}
                  </span>
                ))}
              {blogs.flatMap(blog => blog.keywords).length > 5 && (
                <button
                  onClick={() => setShowAllKeywords(!showAllKeywords)}
                  className="text-orange-500 hover:text-orange-400 text-sm font-medium transition-colors"
                >
                  {showAllKeywords ? 'Less' : 'More'}
                </button>
              )}
            </div>
          </div>

          {/* Any Questions Section */}
          <div className="bg-zinc-900 rounded-[20px] p-6">
            <h3 className="text-white text-[20px] font-bold mb-4">Any Questions?</h3>
            <p className="text-white/70 text-sm mb-4">
              Have questions about our services or need help with your project?
            </p>
            <Link
              href="/contact"
              className="w-full bg-orange-500 hover:bg-orange-600 text-white font-medium py-3 px-4 rounded-lg transition-colors text-center block"
            >
              Let's Talk
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}